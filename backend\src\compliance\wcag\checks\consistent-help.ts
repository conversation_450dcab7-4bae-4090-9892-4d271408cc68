/**
 * WCAG Rule 15: Consistent Help - 3.2.6
 * 80% Automated - Manual review for help content quality and multi-page consistency
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

interface HelpMechanism {
  type: 'contact' | 'phone' | 'email' | 'chat' | 'form' | 'faq' | 'help-text' | 'tutorial';
  element: string;
  text: string;
  position: { x: number; y: number };
  isVisible: boolean;
}

export class ConsistentHelpCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform consistent help check - 80% automated with enhanced evidence
   */
  async performCheck(config: ManualReviewConfig) {
    const result = await this.checkTemplate.executeManualReviewCheck(
      'WCAG-035',
      'Consistent Help',
      'understandable',
      0.05,
      'AA',
      0.8, // 80% automation rate
      config,
      this.executeConsistentHelpCheck.bind(this),
    );

    // Enhanced evidence standardization with consistent help analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-035',
        ruleName: 'Consistent Help',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'consistent-help-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          helpMechanismDetection: true,
          consistencyValidation: true,
          multiPageAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive consistent help analysis
   */
  private async executeConsistentHelpCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze help mechanisms
    const helpMechanisms = await this.analyzeHelpMechanisms(page);

    // Analyze contact information
    const contactAnalysis = await this.analyzeContactInformation(page);

    // Analyze help documentation
    const helpDocAnalysis = await this.analyzeHelpDocumentation(page);

    // Analyze form help
    const formHelpAnalysis = await this.analyzeFormHelp(page);

    // Combine all analyses
    const allAnalyses = [helpMechanisms, contactAnalysis, helpDocAnalysis, formHelpAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 80, // 80% automation as specified for Part 5
    };
  }

  /**
   * Analyze help mechanisms on the page
   */
  private async analyzeHelpMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const helpElements = await page.evaluate(() => {
        const helpSelectors = [
          'a[href*="help"]',
          'a[href*="support"]',
          'a[href*="contact"]',
          'a[href*="faq"]',
          '.help',
          '.support',
          '.contact',
          '.faq',
          '[aria-label*="help"]',
          '[title*="help"]',
          'button[data-help]',
          '.help-button',
          '.support-button',
        ];

        const mechanisms: HelpMechanism[] = [];

        helpSelectors.forEach((selector) => {
          const elements = document.querySelectorAll(selector);
          elements.forEach((element) => {
            const rect = element.getBoundingClientRect();
            const text =
              element.textContent?.trim() ||
              element.getAttribute('aria-label') ||
              element.getAttribute('title') ||
              '';

            if (text && rect.width > 0 && rect.height > 0) {
              let type: HelpMechanism['type'] = 'help-text';

              if (text.toLowerCase().includes('contact') || selector.includes('contact'))
                type = 'contact';
              else if (text.toLowerCase().includes('phone') || text.match(/\d{3}-\d{3}-\d{4}/))
                type = 'phone';
              else if (text.toLowerCase().includes('email') || text.includes('@')) type = 'email';
              else if (text.toLowerCase().includes('chat')) type = 'chat';
              else if (text.toLowerCase().includes('form')) type = 'form';
              else if (text.toLowerCase().includes('faq')) type = 'faq';
              else if (text.toLowerCase().includes('tutorial')) type = 'tutorial';

              mechanisms.push({
                type,
                element: selector,
                text,
                position: { x: rect.left, y: rect.top },
                isVisible: true,
              });
            }
          });
        });

        return mechanisms;
      });

      const totalChecks = 1;
      let passedChecks = 0;

      if (helpElements.length > 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: `Found ${helpElements.length} help mechanism(s)`,
          value: `Help mechanisms count: ${helpElements.length}, Types: ${Array.from(new Set(helpElements.map((h) => h.type))).join(', ')}`,
          element: 'help elements',
        });

        // Group by type for analysis
        const helpByType = helpElements.reduce(
          (acc, help) => {
            if (!acc[help.type]) acc[help.type] = [];
            acc[help.type].push(help);
            return acc;
          },
          {} as Record<string, HelpMechanism[]>,
        );

        // Add manual review for consistency across pages
        manualReviewItems.push({
          selector: 'help mechanisms',
          description: 'Verify help mechanisms are consistently placed across multiple pages',
          automatedFindings: `Help mechanisms found: ${helpElements.length}, Types: ${Object.keys(helpByType).join(', ')}`,
          reviewRequired:
            'Check that help mechanisms appear in the same relative order and location across multiple pages',
          priority: 'high',
          estimatedTime: 10,
          type: 'help_consistency',
          element: 'help mechanisms',
          context: `Help mechanisms found: ${helpElements.length}, Types: ${Object.keys(helpByType).join(', ')}`,
        });
      } else {
        evidence.push({
          type: 'warning',
          description: 'No help mechanisms found on this page',
          value: 'Help mechanisms found: false',
          element: 'page',
        });

        recommendations.push('Consider adding help mechanisms for user assistance');

        manualReviewItems.push({
          selector: 'page',
          description: 'Evaluate if help mechanisms are needed for this page',
          automatedFindings: 'No help mechanisms found on this page',
          reviewRequired: 'Determine if the page complexity requires help mechanisms for users',
          priority: 'medium',
          estimatedTime: 3,
          type: 'help_necessity',
          element: 'page',
          context: 'Page complexity: unknown (to be determined manually)',
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing help mechanisms',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'help elements',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze help mechanisms'],
        recommendations: ['Check help mechanisms manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze contact information consistency
   */
  private async analyzeContactInformation(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const contactInfo = await page.evaluate(() => {
        const phoneRegex = /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;

        const bodyText = document.body.textContent || '';
        const phones = bodyText.match(phoneRegex) || [];
        const emails = bodyText.match(emailRegex) || [];

        // Look for contact links
        const contactLinks = Array.from(
          document.querySelectorAll('a[href*="contact"], a[href*="mailto:"], a[href*="tel:"]'),
        ).map((link) => ({
          href: link.getAttribute('href') || '',
          text: link.textContent?.trim() || '',
          type: link.getAttribute('href')?.startsWith('mailto:')
            ? 'email'
            : link.getAttribute('href')?.startsWith('tel:')
              ? 'phone'
              : 'contact',
        }));

        return {
          phones: Array.from(new Set(phones)),
          emails: Array.from(new Set(emails)),
          contactLinks,
        };
      });

      const totalChecks = 1;
      let passedChecks = 0;

      const hasContactInfo =
        contactInfo.phones.length > 0 ||
        contactInfo.emails.length > 0 ||
        contactInfo.contactLinks.length > 0;

      if (hasContactInfo) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: 'Contact information found on page',
          value: `Phones: ${contactInfo.phones.length}, Emails: ${contactInfo.emails.length}, Contact links: ${contactInfo.contactLinks.length}`,
          element: 'contact information',
        });

        // Add manual review for contact info consistency
        manualReviewItems.push({
          selector: 'contact information',
          description: 'Verify contact information is consistent across all pages',
          automatedFindings: `Phones: ${contactInfo.phones.length}, Emails: ${contactInfo.emails.length}, Contact links: ${contactInfo.contactLinks.length}`,
          reviewRequired:
            'Check that contact information appears consistently across multiple pages',
          priority: 'medium',
          estimatedTime: 5,
          type: 'contact_consistency',
          element: 'contact information',
          context: `Phones: ${contactInfo.phones.length}, Emails: ${contactInfo.emails.length}, Contact links: ${contactInfo.contactLinks.length}`,
        });
      } else {
        evidence.push({
          type: 'info',
          description: 'No contact information found on this page',
          value: 'Contact information found: false',
          element: 'page',
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing contact information',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'contact information',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze contact information'],
        recommendations: ['Check contact information manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze help documentation
   */
  private async analyzeHelpDocumentation(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const helpDocs = await page.$$eval(
        'a[href*="help"], a[href*="documentation"], a[href*="guide"], a[href*="tutorial"]',
        (links) => {
          return links.map((link, index) => ({
            index,
            href: link.getAttribute('href') || '',
            text: link.textContent?.trim() || '',
            isExternal:
              link.getAttribute('href')?.startsWith('http') &&
              !link.getAttribute('href')?.includes(window.location.hostname),
          }));
        },
      );

      const totalChecks = 1;
      let passedChecks = 0;

      if (helpDocs.length > 0) {
        passedChecks++;
        evidence.push({
          type: 'info',
          description: `Found ${helpDocs.length} help documentation link(s)`,
          value: `Help docs count: ${helpDocs.length}, External links: ${helpDocs.filter((doc) => doc.isExternal).length}`,
          element: 'help documentation',
        });

        // Add manual review for documentation quality
        manualReviewItems.push({
          selector: 'help documentation',
          description: 'Review help documentation for completeness and accessibility',
          automatedFindings: `Help docs count: ${helpDocs.length}, Links: ${helpDocs.map((doc) => doc.href).join(', ')}`,
          reviewRequired:
            'Verify that help documentation is complete, accessible, and useful for users',
          priority: 'medium',
          estimatedTime: 8,
          type: 'help_documentation_quality',
          element: 'help documentation',
          context: `Help docs count: ${helpDocs.length}, Links: ${helpDocs.map((doc) => doc.href).join(', ')}`,
        });
      } else {
        evidence.push({
          type: 'info',
          description: 'No help documentation links found',
          value: 'Help documentation found: false',
          element: 'page',
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing help documentation',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'help documentation',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze help documentation'],
        recommendations: ['Check help documentation manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze form help and instructions
   */
  private async analyzeFormHelp(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const formHelp = await page.$$eval('form', (forms) => {
        return forms.map((form, index) => {
          const helpElements = form.querySelectorAll(
            '.help-text, .instruction, [role="note"], .hint',
          );
          const requiredFields = form.querySelectorAll('[required], [aria-required="true"]');
          const errorElements = form.querySelectorAll('.error, .invalid, [aria-invalid="true"]');

          return {
            index,
            hasHelpText: helpElements.length > 0,
            helpElementsCount: helpElements.length,
            requiredFieldsCount: requiredFields.length,
            errorElementsCount: errorElements.length,
            helpTexts: Array.from(helpElements).map((el) => el.textContent?.trim() || ''),
          };
        });
      });

      const totalChecks = formHelp.length;
      let passedChecks = 0;

      if (formHelp.length === 0) {
        evidence.push({
          type: 'info',
          description: 'No forms found to analyze',
          value: 'Forms found: false',
          element: 'page',
        });
        return {
          totalChecks: 1,
          passedChecks: 1,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      formHelp.forEach((form, index) => {
        if (form.requiredFieldsCount === 0 || form.hasHelpText) {
          passedChecks++;
          evidence.push({
            type: 'info',
            description: `Form ${index + 1} has appropriate help text or no required fields`,
            value: `Has help text: ${form.hasHelpText}, Help elements: ${form.helpElementsCount}, Required fields: ${form.requiredFieldsCount}`,
            element: `form:nth-of-type(${index + 1})`,
          });
        } else {
          issues.push(`Form ${index + 1} has required fields but lacks help text`);
          evidence.push({
            type: 'warning',
            description: `Form ${index + 1} may need help text for required fields`,
            value: `Has help text: ${form.hasHelpText}, Required fields: ${form.requiredFieldsCount}`,
            element: `form:nth-of-type(${index + 1})`,
          });
          recommendations.push(`Add help text to form ${index + 1} for required fields`);
        }

        // Add manual review for form help consistency
        if (form.requiredFieldsCount > 0) {
          manualReviewItems.push({
            selector: `form:nth-of-type(${index + 1})`,
            description: `Review form ${index + 1} help text for consistency and clarity`,
            automatedFindings: `Form has ${form.requiredFieldsCount} required fields and ${form.helpElementsCount} help elements`,
            reviewRequired: 'Verify help text is clear, consistent, and accessible',
            priority: 'medium',
            estimatedTime: 4,
            type: 'form_help_consistency',
            element: `form:nth-of-type(${index + 1})`,
            context: `Form ${index + 1}: Required fields: ${form.requiredFieldsCount}, Help elements: ${form.helpElementsCount}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'error',
        description: 'Error analyzing form help',
        value: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        element: 'form',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze form help'],
        recommendations: ['Check form help manually'],
        manualReviewItems,
      };
    }
  }
}
