/**
 * WCAG-027: No Keyboard Trap Check
 * Success Criterion: 2.1.2 No Keyboard Trap (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

interface KeyboardTrapAnalysis {
  interactiveElements: Array<{
    tagName: string;
    selector: string;
    hasTabIndex: boolean;
    tabIndex: number;
    isVisible: boolean;
    hasFocusHandler: boolean;
    hasKeydownHandler: boolean;
    hasKeyupHandler: boolean;
    hasEscapeHandler: boolean;
    role?: string;
    ariaLabel?: string;
  }>;
  potentialTraps: Array<{
    element: string;
    selector: string;
    reason: string;
    severity: 'warning' | 'error';
    hasEscapeRoute: boolean;
    hasInstructions: boolean;
  }>;
  focusableElementCount: number;
  customFocusHandlers: number;
  modalElements: number;
}

export class KeyboardTrapCheck {
  private checkTemplate = new CheckTemplate();

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-027',
      'No Keyboard Trap',
      'operable',
      0.0815,
      'A',
      config,
      this.executeKeyboardTrapCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with keyboard trap analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-047',
        ruleName: 'No Keyboard Trap',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'keyboard-trap-analysis',
          focusManagementValidation: true,
          modalTrapDetection: true,
          escapeRouteValidation: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 30,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'keyboard-trap-detection',
        confidence: 0.85,
        additionalData: {
          checkType: 'keyboard-navigation',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeKeyboardTrapCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze keyboard trap potential
    const trapAnalysis = await page.evaluate((): KeyboardTrapAnalysis => {
      const interactiveSelectors = [
        'a[href]',
        'button',
        'input:not([type="hidden"])',
        'select',
        'textarea',
        '[tabindex]:not([tabindex="-1"])',
        '[role="button"]',
        '[role="link"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="option"]',
        '[contenteditable="true"]'
      ];

      const interactiveElements = document.querySelectorAll(interactiveSelectors.join(', '));
      const elementAnalysis: KeyboardTrapAnalysis['interactiveElements'] = [];
      const potentialTraps: KeyboardTrapAnalysis['potentialTraps'] = [];

      interactiveElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const isVisible = computedStyle.display !== 'none' && 
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';

        const tabIndex = element.getAttribute('tabindex');
        const hasTabIndex = tabIndex !== null;
        const tabIndexValue = hasTabIndex ? parseInt(tabIndex, 10) : 0;

        // Check for event handlers that might create traps
        const hasFocusHandler = element.hasAttribute('onfocus') || 
                               (element as any)._listeners?.focus?.length > 0;
        const hasKeydownHandler = element.hasAttribute('onkeydown') || 
                                 (element as any)._listeners?.keydown?.length > 0;
        const hasKeyupHandler = element.hasAttribute('onkeyup') || 
                               (element as any)._listeners?.keyup?.length > 0;

        // Check for escape key handling
        const hasEscapeHandler = element.hasAttribute('onkeydown') && 
                                element.getAttribute('onkeydown')?.includes('Escape');

        const analysis = {
          tagName: element.tagName.toLowerCase(),
          selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          hasTabIndex,
          tabIndex: tabIndexValue,
          isVisible,
          hasFocusHandler,
          hasKeydownHandler,
          hasKeyupHandler,
          hasEscapeHandler,
          role: element.getAttribute('role') || undefined,
          ariaLabel: element.getAttribute('aria-label') || undefined,
        };

        elementAnalysis.push(analysis);

        // Detect potential keyboard traps
        if (isVisible && (hasKeydownHandler || hasKeyupHandler || hasFocusHandler)) {
          // Check for modal-like behavior
          const isModal = element.getAttribute('role') === 'dialog' ||
                         element.getAttribute('aria-modal') === 'true' ||
                         element.classList.contains('modal') ||
                         element.classList.contains('dialog');

          // Check for custom focus management
          const hasCustomFocus = hasKeydownHandler && 
                                (element.getAttribute('onkeydown')?.includes('focus') ||
                                 element.getAttribute('onkeydown')?.includes('Tab'));

          if (isModal || hasCustomFocus) {
            const hasEscapeRoute = hasEscapeHandler || 
                                  element.querySelector('[aria-label*="close"]') !== null ||
                                  element.querySelector('.close') !== null ||
                                  element.querySelector('[data-dismiss]') !== null;

            const hasInstructions = element.querySelector('[aria-describedby]') !== null ||
                                   element.getAttribute('aria-describedby') !== null ||
                                   element.textContent?.toLowerCase().includes('press escape') ||
                                   element.textContent?.toLowerCase().includes('close');

            potentialTraps.push({
              element: element.tagName.toLowerCase(),
              selector: analysis.selector,
              reason: isModal ? 'Modal dialog without proper escape mechanism' : 
                     'Custom focus management detected',
              severity: (!hasEscapeRoute && !hasInstructions) ? 'error' : 'warning',
              hasEscapeRoute,
              hasInstructions,
            });
          }
        }
      });

      // Count different types of elements
      const focusableElementCount = elementAnalysis.filter(el => el.isVisible).length;
      const customFocusHandlers = elementAnalysis.filter(el => 
        el.hasFocusHandler || el.hasKeydownHandler || el.hasKeyupHandler
      ).length;
      const modalElements = document.querySelectorAll('[role="dialog"], [aria-modal="true"], .modal').length;

      return {
        interactiveElements: elementAnalysis,
        potentialTraps,
        focusableElementCount,
        customFocusHandlers,
        modalElements,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = trapAnalysis.focusableElementCount;

    // Analyze potential keyboard traps
    if (trapAnalysis.potentialTraps.length > 0) {
      const errorTraps = trapAnalysis.potentialTraps.filter(trap => trap.severity === 'error');
      const warningTraps = trapAnalysis.potentialTraps.filter(trap => trap.severity === 'warning');

      if (errorTraps.length > 0) {
        score = 0; // Critical failure for confirmed traps
        
        errorTraps.forEach((trap, index) => {
          issues.push(`Keyboard trap detected: ${trap.reason}`);
          
          evidence.push({
            type: 'error',
            description: 'Potential keyboard trap detected',
            value: `Element: ${trap.element} - ${trap.reason}`,
            selector: trap.selector,
            elementCount: 1,
            affectedSelectors: [trap.selector],
            severity: 'error',
            fixExample: {
              before: `<div role="dialog">Content without escape mechanism</div>`,
              after: `<div role="dialog" aria-modal="true">
  <button aria-label="Close dialog" onclick="closeDialog()">×</button>
  Content with escape mechanism
</div>`,
              description: 'Provide escape mechanism for modal dialogs',
              codeExample: `
// Add keyboard event handler for escape key
element.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    closeDialog();
  }
});

// Or provide visible close button
<button aria-label="Close dialog" onclick="closeDialog()">
  Close
</button>
              `,
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/no-keyboard-trap.html',
                'https://www.w3.org/WAI/ARIA/apg/patterns/dialogmodal/',
                'https://developer.mozilla.org/en-US/docs/Web/Accessibility/Keyboard-navigable_JavaScript_widgets'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                trapType: trap.reason,
                hasEscapeRoute: trap.hasEscapeRoute,
                hasInstructions: trap.hasInstructions,
                severity: trap.severity,
              },
            },
          });
        });
      }

      if (warningTraps.length > 0) {
        score = Math.max(score - (warningTraps.length * 10), 60); // Reduce score for warnings
        
        warningTraps.forEach((trap) => {
          issues.push(`Potential keyboard trap: ${trap.reason}`);
          
          evidence.push({
            type: 'warning',
            description: 'Potential keyboard navigation issue',
            value: `Element: ${trap.element} - ${trap.reason}`,
            selector: trap.selector,
            elementCount: 1,
            affectedSelectors: [trap.selector],
            severity: 'warning',
            fixExample: {
              before: `<div>Custom focus handling without clear escape</div>`,
              after: `<div>
  <!-- Provide clear instructions -->
  <p id="instructions">Press Escape to exit this component</p>
  <div aria-describedby="instructions">Custom focus handling with escape</div>
</div>`,
              description: 'Provide clear escape instructions for custom focus management',
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/no-keyboard-trap.html'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                trapType: trap.reason,
                hasEscapeRoute: trap.hasEscapeRoute,
                hasInstructions: trap.hasInstructions,
                severity: trap.severity,
              },
            },
          });
        });
      }
    }

    // Add positive evidence for good keyboard navigation
    if (score > 80 && totalElements > 0) {
      evidence.push({
        type: 'info',
        description: 'No keyboard traps detected',
        value: `Analyzed ${totalElements} focusable elements with no keyboard traps found`,
        selector: 'body',
        elementCount: totalElements,
        affectedSelectors: ['*'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: totalElements,
          checkSpecificData: {
            focusableElements: trapAnalysis.focusableElementCount,
            customFocusHandlers: trapAnalysis.customFocusHandlers,
            modalElements: trapAnalysis.modalElements,
            potentialTraps: trapAnalysis.potentialTraps.length,
          },
        },
      });
    }

    // Generate recommendations
    if (score < 100) {
      recommendations.push('Ensure all interactive components allow keyboard navigation away');
      recommendations.push('Provide escape mechanisms (Escape key, close buttons) for modal dialogs');
      recommendations.push('Include clear instructions for custom keyboard interactions');
      recommendations.push('Test keyboard navigation thoroughly, especially in modal dialogs');
    } else {
      recommendations.push('Continue testing keyboard navigation in dynamic content');
      recommendations.push('Ensure new interactive components maintain keyboard accessibility');
    }

    if (trapAnalysis.modalElements > 0) {
      recommendations.push('Verify modal dialogs implement proper focus management');
      recommendations.push('Test that Escape key closes modal dialogs');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
