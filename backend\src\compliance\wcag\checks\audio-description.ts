/**
 * WCAG-034: Audio Description Check
 * Success Criterion: 1.2.3 Audio Description or Media Alternative (Prerecorded) (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckTemplate, CheckConfig } from '../utils/check-template';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

interface VideoElement {
  selector: string;
  src?: string;
  hasAudio: boolean;
  hasVideo: boolean;
  hasAudioDescription: boolean;
  hasMediaAlternative: boolean;
  audioDescriptionType?: 'track' | 'separate' | 'integrated' | 'none';
  mediaAlternativeType?: 'transcript' | 'text-description' | 'none';
  trackElements: Array<{
    kind: string;
    src: string;
    label?: string;
    srclang?: string;
  }>;
  alternativeText?: string;
  isPrerecorded: boolean;
  isDecorative: boolean;
  duration?: number;
}

interface AudioDescriptionAnalysis {
  videoElements: VideoElement[];
  videosNeedingDescription: VideoElement[];
  videosWithDescription: VideoElement[];
  videosWithAlternative: VideoElement[];
  totalVideos: number;
  videosWithAudioDescriptionOrAlternative: number;
  videosWithoutRequiredDescription: number;
}

export class AudioDescriptionCheck {
  private checkTemplate = new CheckTemplate();

  // Keywords that indicate audio description
  private readonly audioDescriptionIndicators = [
    'audio description', 'described video', 'video description', 'descriptive audio',
    'narration', 'described', 'audio narration', 'visual description'
  ];

  // Keywords that indicate media alternatives
  private readonly mediaAlternativeIndicators = [
    'transcript', 'text version', 'full transcript', 'media alternative',
    'text description', 'detailed description', 'script', 'narrative text'
  ];

  // Keywords that indicate decorative content
  private readonly decorativeIndicators = [
    'background video', 'decorative', 'ambient', 'loop', 'autoplay',
    'banner video', 'hero video', 'mood video'
  ];

  async performCheck(config: CheckConfig): Promise<WcagCheckResultEnhanced> {
    const result = await this.checkTemplate.executeCheck(
      'WCAG-034',
      'Audio Description',
      'perceivable',
      0.0815,
      'A',
      config,
      this.executeAudioDescriptionCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with audio description analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-028',
        ruleName: 'Audio Description',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'audio-description-analysis',
          videoAnalysis: true,
          trackElementAnalysis: true,
          mediaAlternativeDetection: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 25,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'audio-description-analysis',
        confidence: 0.60,
        additionalData: {
          checkType: 'media-accessibility',
          automationLevel: 'medium',
        },
      },
    };
  }

  private async executeAudioDescriptionCheck(
    page: Page,
    config: CheckConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze video elements for audio description requirements
    const descriptionAnalysis = await page.evaluate(() => {
      const videoElements = document.querySelectorAll('video');
      const iframeElements = document.querySelectorAll('iframe[src*="youtube"], iframe[src*="vimeo"]');
      
      const videoAnalysis: VideoElement[] = [];

      // Audio description indicators
      const audioDescriptionIndicators = [
        'audio description', 'described video', 'video description', 'descriptive audio',
        'narration', 'described', 'audio narration', 'visual description'
      ];

      const mediaAlternativeIndicators = [
        'transcript', 'text version', 'full transcript', 'media alternative',
        'text description', 'detailed description', 'script', 'narrative text'
      ];

      const decorativeIndicators = [
        'background video', 'decorative', 'ambient', 'loop', 'autoplay',
        'banner video', 'hero video', 'mood video'
      ];

      // Helper function to find audio description or media alternative
      function analyzeVideoAccessibility(element: Element): {
        hasAudioDescription: boolean;
        hasMediaAlternative: boolean;
        audioDescriptionType?: 'track' | 'separate' | 'integrated' | 'none';
        mediaAlternativeType?: 'transcript' | 'text-description' | 'none';
        alternativeText?: string;
      } {
        // Check for track elements
        const trackElements = element.querySelectorAll('track');
        let hasAudioDescriptionTrack = false;
        
        trackElements.forEach(track => {
          const kind = track.getAttribute('kind');
          const label = track.getAttribute('label')?.toLowerCase() || '';
          
          if (kind === 'descriptions' || 
              audioDescriptionIndicators.some(indicator => label.includes(indicator))) {
            hasAudioDescriptionTrack = true;
          }
        });

        // Check surrounding content for alternatives
        const container = element.closest('section, article, div, figure') || element.parentElement;
        const containerText = container?.textContent?.toLowerCase() || '';
        const containerHTML = container?.innerHTML?.toLowerCase() || '';

        // Check for audio description references
        const hasAudioDescriptionRef = audioDescriptionIndicators.some(indicator => 
          containerText.includes(indicator) || containerHTML.includes(indicator)
        );

        // Check for media alternatives
        const hasMediaAlternativeRef = mediaAlternativeIndicators.some(indicator => 
          containerText.includes(indicator) || containerHTML.includes(indicator)
        );

        let audioDescriptionType: 'track' | 'separate' | 'integrated' | 'none' = 'none';
        let mediaAlternativeType: 'transcript' | 'text-description' | 'none' = 'none';
        let alternativeText = '';

        if (hasAudioDescriptionTrack) {
          audioDescriptionType = 'track';
        } else if (hasAudioDescriptionRef) {
          audioDescriptionType = 'separate';
        }

        if (hasMediaAlternativeRef) {
          if (containerText.includes('transcript') || containerHTML.includes('transcript')) {
            mediaAlternativeType = 'transcript';
          } else {
            mediaAlternativeType = 'text-description';
          }
          alternativeText = container?.textContent?.trim().substring(0, 200) || '';
        }

        return {
          hasAudioDescription: hasAudioDescriptionTrack || hasAudioDescriptionRef,
          hasMediaAlternative: hasMediaAlternativeRef,
          audioDescriptionType,
          mediaAlternativeType,
          alternativeText,
        };
      }

      // Helper function to check if video is decorative
      function isDecorativeVideo(element: Element): boolean {
        const elementAttrs = element.outerHTML.toLowerCase();
        const parentText = element.parentElement?.textContent?.toLowerCase() || '';
        const ariaHidden = element.getAttribute('aria-hidden') === 'true';
        const role = element.getAttribute('role');

        if (ariaHidden || role === 'presentation') {
          return true;
        }

        // Check for autoplay, loop, muted (often decorative)
        const hasAutoplay = element.hasAttribute('autoplay');
        const hasLoop = element.hasAttribute('loop');
        const isMuted = element.hasAttribute('muted');

        if (hasAutoplay && hasLoop && isMuted) {
          return true;
        }

        return decorativeIndicators.some(indicator => 
          elementAttrs.includes(indicator) || parentText.includes(indicator)
        );
      }

      // Analyze video elements
      videoElements.forEach((video, index) => {
        const accessibility = analyzeVideoAccessibility(video);
        const isDecorative = isDecorativeVideo(video);
        
        // Check if video has audio (limited detection in browser)
        const hasAudio = !video.hasAttribute('muted') && 
                         video.textContent?.toLowerCase().includes('audio') !== false;
        
        const trackElements = Array.from(video.querySelectorAll('track')).map(track => ({
          kind: track.getAttribute('kind') || '',
          src: track.getAttribute('src') || '',
          label: track.getAttribute('label') || undefined,
          srclang: track.getAttribute('srclang') || undefined,
        }));

        videoAnalysis.push({
          selector: `video:nth-of-type(${index + 1})`,
          src: video.getAttribute('src') || video.querySelector('source')?.getAttribute('src'),
          hasAudio,
          hasVideo: true,
          hasAudioDescription: accessibility.hasAudioDescription,
          hasMediaAlternative: accessibility.hasMediaAlternative,
          audioDescriptionType: accessibility.audioDescriptionType,
          mediaAlternativeType: accessibility.mediaAlternativeType,
          trackElements,
          alternativeText: accessibility.alternativeText,
          isPrerecorded: !video.hasAttribute('live'),
          isDecorative,
        });
      });

      // Analyze iframe video elements (limited analysis)
      iframeElements.forEach((iframe, index) => {
        const src = iframe.getAttribute('src') || '';
        const container = iframe.closest('section, article, div, figure') || iframe.parentElement;
        const containerText = container?.textContent?.toLowerCase() || '';
        
        const hasMediaAlternative = mediaAlternativeIndicators.some(indicator => 
          containerText.includes(indicator)
        );

        const isDecorative = decorativeIndicators.some(indicator => 
          containerText.includes(indicator)
        );

        videoAnalysis.push({
          selector: `iframe:nth-of-type(${index + 1})`,
          src,
          hasAudio: true, // Assume iframe videos have audio
          hasVideo: true,
          hasAudioDescription: false, // Cannot detect in iframe
          hasMediaAlternative,
          audioDescriptionType: 'none',
          mediaAlternativeType: hasMediaAlternative ? 'transcript' : 'none',
          trackElements: [],
          alternativeText: container?.textContent?.trim().substring(0, 200),
          isPrerecorded: true,
          isDecorative,
        });
      });

      // Filter videos that need description (prerecorded videos with audio and video, not decorative)
      const videosNeedingDescription = videoAnalysis.filter(video => 
        video.hasAudio && video.hasVideo && video.isPrerecorded && !video.isDecorative
      );

      const videosWithDescription = videosNeedingDescription.filter(video => 
        video.hasAudioDescription
      );

      const videosWithAlternative = videosNeedingDescription.filter(video => 
        video.hasMediaAlternative
      );

      const videosWithAudioDescriptionOrAlternative = videosNeedingDescription.filter(video => 
        video.hasAudioDescription || video.hasMediaAlternative
      );

      const videosWithoutRequiredDescription = videosNeedingDescription.filter(video => 
        !video.hasAudioDescription && !video.hasMediaAlternative
      );

      return {
        videoElements: videoAnalysis,
        videosNeedingDescription,
        videosWithDescription,
        videosWithAlternative,
        totalVideos: videoAnalysis.length,
        videosWithAudioDescriptionOrAlternative: videosWithAudioDescriptionOrAlternative.length,
        videosWithoutRequiredDescription: videosWithoutRequiredDescription.length,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = descriptionAnalysis.totalVideos;

    if (totalElements === 0) {
      // No video elements found
      evidence.push({
        type: 'info',
        description: 'No video content requiring audio description found',
        value: 'Page contains no prerecorded video content with audio',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalVideos: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future video content includes audio description or media alternatives'],
      };
    }

    // Check if there are videos that need description
    if (descriptionAnalysis.videosNeedingDescription.length === 0) {
      evidence.push({
        type: 'info',
        description: 'No videos requiring audio description found',
        value: `${totalElements} video elements found, but none require audio description (decorative or audio-only)`,
        selector: 'video, iframe',
        elementCount: totalElements,
        affectedSelectors: ['video', 'iframe'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: totalElements,
          checkSpecificData: {
            totalVideos: descriptionAnalysis.totalVideos,
            videosNeedingDescription: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Continue ensuring video content accessibility'],
      };
    }

    // Analyze videos without required description
    if (descriptionAnalysis.videosWithoutRequiredDescription.length > 0) {
      const failureRate = descriptionAnalysis.videosWithoutRequiredDescription.length / 
                         descriptionAnalysis.videosNeedingDescription.length;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      descriptionAnalysis.videosWithoutRequiredDescription.forEach((video) => {
        issues.push(`Video without audio description or media alternative: ${video.selector}`);
        
        evidence.push({
          type: 'error',
          description: 'Video content missing audio description or media alternative',
          value: `Video element requires audio description or equivalent text alternative`,
          selector: video.selector,
          elementCount: 1,
          affectedSelectors: [video.selector],
          severity: 'error',
          fixExample: {
            before: '<video controls src="presentation.mp4"></video>',
            after: `<video controls src="presentation.mp4">
  <track kind="descriptions" src="presentation-desc.vtt" srclang="en" label="Audio descriptions">
</video>
<div class="media-alternative">
  <h3>Video Description</h3>
  <p>Detailed text description of visual content...</p>
</div>`,
            description: 'Provide audio description track or equivalent media alternative',
            codeExample: `
<!-- Option 1: Audio description track -->
<video controls src="lecture.mp4">
  <track kind="descriptions" src="lecture-descriptions.vtt" srclang="en" label="Audio descriptions">
  <track kind="captions" src="lecture-captions.vtt" srclang="en" label="Captions">
</video>

<!-- Option 2: Media alternative (detailed text) -->
<video controls src="demo.mp4" aria-describedby="video-alternative"></video>
<div id="video-alternative">
  <h3>Video Content Description</h3>
  <p>The video shows a step-by-step demonstration of...</p>
  <p>At 0:30, the presenter clicks on the menu button...</p>
  <p>At 1:15, a dialog box appears with three options...</p>
</div>

<!-- Option 3: Full transcript with timing -->
<video controls src="interview.mp4"></video>
<details>
  <summary>Full Video Transcript with Descriptions</summary>
  <p>[0:00] Host sits at desk, smiles at camera</p>
  <p>[0:05] <strong>Host:</strong> Welcome to our show...</p>
  <p>[0:30] Guest enters from left, waves to audience</p>
</details>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/audio-description-or-media-alternative-prerecorded.html',
              'https://www.w3.org/WAI/media/av/description/',
              'https://webaim.org/techniques/captions/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              hasAudio: video.hasAudio,
              hasVideo: video.hasVideo,
              isPrerecorded: video.isPrerecorded,
              trackElements: video.trackElements.length,
              src: video.src,
              audioDescriptionType: video.audioDescriptionType,
              mediaAlternativeType: video.mediaAlternativeType,
            },
          },
        });
      });
    }

    // Add positive evidence for videos with description
    if (descriptionAnalysis.videosWithDescription.length > 0) {
      evidence.push({
        type: 'info',
        description: 'Videos with audio description found',
        value: `${descriptionAnalysis.videosWithDescription.length} videos have audio description tracks`,
        selector: 'video',
        elementCount: descriptionAnalysis.videosWithDescription.length,
        affectedSelectors: ['video'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: descriptionAnalysis.videosWithDescription.length,
          checkSpecificData: {
            videosWithDescription: descriptionAnalysis.videosWithDescription.length,
          },
        },
      });
    }

    // Add positive evidence for videos with media alternatives
    if (descriptionAnalysis.videosWithAlternative.length > 0) {
      evidence.push({
        type: 'info',
        description: 'Videos with media alternatives found',
        value: `${descriptionAnalysis.videosWithAlternative.length} videos have text alternatives`,
        selector: 'video',
        elementCount: descriptionAnalysis.videosWithAlternative.length,
        affectedSelectors: ['video'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: descriptionAnalysis.videosWithAlternative.length,
          checkSpecificData: {
            videosWithAlternative: descriptionAnalysis.videosWithAlternative.length,
          },
        },
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Audio description analysis summary',
      value: `${descriptionAnalysis.videosNeedingDescription.length} videos need description: ${descriptionAnalysis.videosWithAudioDescriptionOrAlternative.length} compliant, ${descriptionAnalysis.videosWithoutRequiredDescription.length} missing`,
      selector: 'video, iframe',
      elementCount: descriptionAnalysis.videosNeedingDescription.length,
      affectedSelectors: ['video', 'iframe'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: descriptionAnalysis.videosNeedingDescription.length,
        checkSpecificData: {
          totalVideos: descriptionAnalysis.totalVideos,
          videosNeedingDescription: descriptionAnalysis.videosNeedingDescription.length,
          videosWithDescription: descriptionAnalysis.videosWithDescription.length,
          videosWithAlternative: descriptionAnalysis.videosWithAlternative.length,
          videosWithAudioDescriptionOrAlternative: descriptionAnalysis.videosWithAudioDescriptionOrAlternative.length,
          videosWithoutRequiredDescription: descriptionAnalysis.videosWithoutRequiredDescription.length,
          complianceRate: descriptionAnalysis.videosNeedingDescription.length > 0 
            ? (descriptionAnalysis.videosWithAudioDescriptionOrAlternative.length / descriptionAnalysis.videosNeedingDescription.length * 100).toFixed(1)
            : '100',
        },
      },
    });

    // Generate recommendations
    if (descriptionAnalysis.videosWithoutRequiredDescription.length > 0) {
      recommendations.push('Provide audio description tracks for all prerecorded videos with visual content');
      recommendations.push('Alternatively, provide detailed text descriptions as media alternatives');
      recommendations.push('Use <track kind="descriptions"> elements for audio description');
      recommendations.push('Ensure descriptions cover all important visual information');
    } else {
      recommendations.push('Continue providing audio descriptions for video content');
      recommendations.push('Test audio descriptions with users who are blind or have low vision');
    }

    if (descriptionAnalysis.videosWithDescription.length > 0) {
      recommendations.push('Verify that audio descriptions are synchronized with video content');
    }

    if (descriptionAnalysis.videosWithAlternative.length > 0) {
      recommendations.push('Ensure text alternatives are equivalent to the video content');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
